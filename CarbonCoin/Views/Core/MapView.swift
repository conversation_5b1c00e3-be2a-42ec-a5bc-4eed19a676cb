//
//  MapView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/20.
//

import SwiftUI
import MapKit
import CoreLocation
import PopupView

/// 为系统类型 CLLocationCoordinate2D 添加 Equatable 扩展
extension CLLocationCoordinate2D: @retroactive Equatable {
    public static func == (lhs: CLLocationCoordinate2D, rhs: CLLocationCoordinate2D) -> Bool {
        lhs.latitude == rhs.latitude && lhs.longitude == rhs.longitude
    }
}

// MARK: - 地图视图组件
struct MapView: View {
    @StateObject private var viewModel: MapViewModel
    @StateObject private var friendMapViewModel: FriendMapViewModel
    @StateObject private var cardTransferViewModel = CardTransferViewModel()
    @StateObject private var propInteractionViewModel : PropInteractionViewModel

    // 获取当前用户Id
    @AppStorage("currentUserId") private var currentUserId: String = ""

    @EnvironmentObject private var appSettings: AppSettings
    @Environment(\.dismiss) private var dismiss
    @State private var showLocationPermissionAlert = false

    // MARK: 卡片和道具显示状态
    @State private var isAnimating = false
    @State private var refreshTask: Task<Void, Never>?
    @State private var showCardOverlay = false
    @State private var showPropOverlay = false


    // MARK: - 卡片传输状态
    @State private var isDraggingCard = false
    @State private var draggedCard: ItemCard?
    @State private var dragPosition: CGPoint = .zero
    @State private var dragStartPosition: CGPoint = .zero
    @State private var showTransferSheet = false

    /// 用于绑定自定义的alert
    @State private var showDropAlert = false
    /// 存储待传输的卡片ID
    @State private var pendingCardId: String?
    /// 存储待传输的接收者ID
    @State private var pendingReceiverId: String?

    // MARK: - 道具传输状态
    @State private var isDraggingProp = false
    @State private var draggedProp: PropInfo?
    @State private var propDragPosition: CGPoint = .zero
    @State private var propDragStartPosition: CGPoint = .zero
    /// 存储待传输的道具ID
    @State private var pendingPropId: Int?
    /// 存储待传输的接收者ID
    @State private var pendingPropReceiverId: String?

    // MARK: - 地点打卡相关状态
    @EnvironmentObject private var placeCheckinViewModel : PlaceCheckinViewModel
    
    @State private var showCheckinSheet = false
    @State private var showFootprintSheet = false
    @State private var showCheckinAnnotations = true

    // MARK: - 选中好友状态管理
    /// 当前选中的好友ID（用于背景变暗和头像高亮效果）
    @State private var selectedFriendId: String?
    /// 是否显示背景遮罩（当有sheet弹出时）
    @State private var showBackgroundOverlay = false

    // MARK: - 视图状态管理
    /// 是否已经完成初始加载
    @State private var hasInitialLoaded = false

    init() {
        self._viewModel = StateObject(wrappedValue: MapViewModel())
        self._friendMapViewModel = StateObject(wrappedValue: FriendMapViewModel())

        // 使用当前用户Id初始化道具管理viewmodel
        let userId = UserDefaults.standard.string(forKey: "currentUserId") ?? ""
        self._propInteractionViewModel = StateObject(wrappedValue: PropInteractionViewModel(currentUserId: userId))
    }

    // MARK: Body
    var body: some View {
        ZStack {
            mapContent
        }
        .overlay(alignment: .bottom) {
            // 卡片覆盖层视图
            if showCardOverlay {
                Spacer()

                ItemCardSheetView(
                    onCardDropped: { cardId, userId in
                        handleCardDropToFriend(cardId: cardId, userId: userId)
                    },
                    onGlobalDragStarted: { card, position in
                        startGlobalDrag(card: card, startPosition: position)
                    },
                    onGlobalDragChanged: { position in
                        updateGlobalDrag(to: position)
                    },
                    onGlobalDragEnded: {
                        endGlobalDrag()
                    }
                )
                .background(Color.clear.ignoresSafeArea(edges: .bottom))
                .environmentObject(CardStore())
                .frame(maxHeight: UIScreen.main.bounds.height / 4)
                .transition(.move(edge: .bottom))
                .animation(.easeInOut(duration: 0.3), value: showCardOverlay)
            }

            // 道具覆盖层视图
            if showPropOverlay {
                Spacer()

                PropTransferSheet(
                    onPropDropped: { propId, userId in
                        handlePropDropToFriend(propId: propId, userId: userId)
                    },
                    onGlobalDragStarted: { prop, position in
                        startGlobalPropDrag(prop: prop, startPosition: position)
                    },
                    onGlobalDragChanged: { position in
                        updateGlobalPropDrag(to: position)
                    },
                    onGlobalDragEnded: {
                        endGlobalPropDrag()
                    }
                )
                .background(Color.clear.ignoresSafeArea(edges: .bottom))
                .frame(maxHeight: UIScreen.main.bounds.height / 4)
                .transition(.move(edge: .bottom))
                .animation(.easeInOut(duration: 0.3), value: showPropOverlay)
            }
        }
        .ignoresSafeArea()
        .toolbar(.hidden, for: .tabBar) // 隐藏TabBar，因为地图是全屏显示
        .alert("位置权限", isPresented: $showLocationPermissionAlert) {
            Button("去设置") {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    UIApplication.shared.open(settingsUrl)
                }
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text(viewModel.errorMessage ?? "需要位置权限才能显示您的当前位置")
        }
        .alert("好友位置", isPresented: .constant(friendMapViewModel.errorMessage != nil)) {
            Button("确定") {
                friendMapViewModel.clearMessages()
            }
        } message: {
            if let errorMessage = friendMapViewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .onAppear {
            viewModel.startLocationUpdates()
            isAnimating = true
            
            // 清除之前的错误信息，避免显示过期的错误
            friendMapViewModel.clearMessages()

            // 只在首次加载时获取好友位置信息，避免从其他视图返回时重复加载
            if !hasInitialLoaded {
                // 立即获取好友位置信息
                Task{
                    await friendMapViewModel.loadFriendLocations(for: appSettings.userId)
                }
                
                hasInitialLoaded = true
            }

            // 加载地点打卡记录
            Task {
                await placeCheckinViewModel.loadCheckinsList(userId: appSettings.userId)
            }

            // 启动道具交互消息轮询
            propInteractionViewModel.startPollingUnreadMessages()

            // 定时更新好友位置信息
            refreshTask = Task {
                while !Task.isCancelled {
                    try? await Task.sleep(for: .seconds(60))
                    await friendMapViewModel.refreshFriendLocations(for: appSettings.userId)
                    print("定时自动更新好友位置信息")
                }
            }
        }
        .onDisappear {
            viewModel.stopLocationUpdates()
            refreshTask?.cancel()
            refreshTask = nil
            // 停止道具交互消息轮询
            propInteractionViewModel.stopPollingUnreadMessages()
            friendMapViewModel.clearMessages()
        }
        .onChange(of: viewModel.errorMessage) { _, newValue in
            if newValue != nil {
                showLocationPermissionAlert = true
            }
        }
        .sheet(isPresented: $showTransferSheet) {
            CardTransferSheet()
        }
        .sheet(isPresented: $showCheckinSheet){
            CheckinSheet(showCheckinAnnotations: $showCheckinAnnotations)
                .presentationDetents([.height(400), .fraction(0.95)])
                .presentationCornerRadius(40)
                .presentationDragIndicator(.hidden)
        }
        .sheet(isPresented: $showFootprintSheet){
            FootprintSheet()
                .presentationDetents([.fraction(0.95)])
                .presentationCornerRadius(40)
                .presentationDragIndicator(.hidden)
        }
        .alert("传输成功", isPresented: .constant(cardTransferViewModel.successMessage != nil)) {
            Button("确定") {
                cardTransferViewModel.clearMessages()
            }
        } message: {
            if let successMessage = cardTransferViewModel.successMessage {
                Text(successMessage)
            }
        }
        .alert("传输失败", isPresented: .constant(cardTransferViewModel.errorMessage != nil)) {
            Button("确定") {
                cardTransferViewModel.clearMessages()
            }
        } message: {
            if let errorMessage = cardTransferViewModel.errorMessage {
                Text(errorMessage)
            }
        }
        // MARK: 自定义确认弹窗
        .overlay{
            CustomAlert(
                isPresented: $showDropAlert,
                title: pendingPropId != nil ? "确认分享道具" : "确认分享卡片",
                message: "您确定要将\(pendingPropId != nil ? "道具" : "卡片")分享给 \(friendMapViewModel.friendAnnotations.first(where: { $0.friendLocation.userId == (pendingPropId != nil ? pendingPropReceiverId : pendingReceiverId) })?.friendLocation.nickname ?? "该好友") 吗？",
                confirmTitle:"分享",
                cancelTitle:"再想想",
                onConfirm: {
                    if pendingPropId != nil {
                        confirmPropDropToFriend()
                    } else {
                        confirmCardDropToFriend()
                    }
                },
                onCancel: {
                    // 用户取消传输，清除待传输信息
                    if pendingPropId != nil {
                        pendingPropId = nil
                        pendingPropReceiverId = nil
                    } else {
                        pendingCardId = nil
                        pendingReceiverId = nil
                    }
                },
            )
        }
        // MARK: 道具消息弹窗
        .popup(isPresented: $propInteractionViewModel.showNewMessagePopup) {
            if let latestMessage = propInteractionViewModel.latestUnreadMessage {
                PopupPropSheet(interaction: latestMessage)
            }
        } customize: {
            $0.position(.center)
                .closeOnTap(true)
                .backgroundColor(.black.opacity(0.4))
                .dismissCallback{
                    Task{
                        if let latestMessage = propInteractionViewModel.latestUnreadMessage{
                            await propInteractionViewModel.markAsRead(interactionId: latestMessage.id)
                        }
                    }

                    propInteractionViewModel.showNewMessagePopup = false
                }
        }
    }

    // MARK: - 全局拖拽处理函数

    /// 开始全局拖拽
    private func startGlobalDrag(card: ItemCard, startPosition: CGPoint) {
        isDraggingCard = true
        draggedCard = card
        dragStartPosition = startPosition
        dragPosition = startPosition
        print("🎯 开始全局拖拽: \(card.title)")
    }

    /// 更新拖拽位置
    private func updateGlobalDrag(to position: CGPoint) {
        dragPosition = position
    }

    /// 结束全局拖拽
    private func endGlobalDrag() {
        isDraggingCard = false
        draggedCard = nil
        dragPosition = .zero
        dragStartPosition = .zero
    }

    // MARK: - 道具全局拖拽处理函数

    /// 开始道具全局拖拽
    private func startGlobalPropDrag(prop: PropInfo, startPosition: CGPoint) {
        isDraggingProp = true
        draggedProp = prop
        propDragStartPosition = startPosition
        propDragPosition = startPosition
        print("🎯 开始全局拖拽道具: \(prop.propTitle)")
    }

    /// 更新道具拖拽位置
    private func updateGlobalPropDrag(to position: CGPoint) {
        propDragPosition = position
    }

    /// 结束道具全局拖拽
    private func endGlobalPropDrag() {
        isDraggingProp = false
        draggedProp = nil
        propDragPosition = .zero
        propDragStartPosition = .zero
    }


    // MARK: - 卡片拖放处理函数

    /// 处理卡片拖放到好友位置
    private func handleCardDropToFriend(cardId: String, userId: String) {
        print("🎯 卡片 \(cardId) 被拖放到好友 \(userId)")

        // 保存待传输信息
        pendingCardId = cardId
        pendingReceiverId = userId

        // 显示确认弹窗
        showDropAlert = true
    }

    /// 确认卡片拖放到好友位置并执行传输
    private func confirmCardDropToFriend() {
        guard let cardId = pendingCardId, let userId = pendingReceiverId else {
            print("❌ 缺少卡片或接收者信息，无法传输。")
            return
        }

        // 获取好友信息用于显示
        let friendName = friendMapViewModel.friendAnnotations.first(where: { $0.friendLocation.userId == userId })?.friendLocation.nickname ?? "未知好友"

        // 调用传输API
        Task {
            await cardTransferViewModel.createCardTransfer(cardId: cardId, receiverId: userId)
        }

        print("✅ 卡片传输请求已发送给 \(friendName)")

        // 关闭sheet并清除选中状态
        closeSheets()

        // 清除待传输信息
        pendingCardId = nil
        pendingReceiverId = nil
    }

    // MARK: - 道具拖放处理函数

    /// 处理道具拖放到好友位置
    private func handlePropDropToFriend(propId: Int, userId: String) {
        print("🎯 道具 \(propId) 被拖放到好友 \(userId)")

        // 保存待传输信息
        pendingPropId = propId
        pendingPropReceiverId = userId

        // 显示确认弹窗（这里可以复用现有的弹窗或创建新的）
        showDropAlert = true
    }

    /// 确认道具拖放到好友位置并执行传输
    private func confirmPropDropToFriend() {
        guard let propId = pendingPropId, let userId = pendingPropReceiverId else {
            print("❌ 缺少道具或接收者信息，无法传输。")
            return
        }

        // 获取好友信息用于显示
        let friendName = friendMapViewModel.friendAnnotations.first(where: { $0.friendLocation.userId == userId })?.friendLocation.nickname ?? "未知好友"

        // 调用道具传输API
        Task {
            await propInteractionViewModel.createPropInteraction(receiverUserId: userId, propId: propId, remark: "")
        }

        print("✅ 道具传输请求已发送给 \(friendName)")

        // 关闭sheet并清除选中状态
        closeSheets()

        // 清除待传输信息
        pendingPropId = nil
        pendingPropReceiverId = nil
    }

    // MARK: - Sheet管理方法

    /// 关闭所有sheet并清除选中的好友状态
    private func closeSheets() {
        withAnimation(.easeInOut(duration: 0.3)) {
            showCardOverlay = false
            showPropOverlay = false
            showBackgroundOverlay = false
        }

        // 清除选中的好友ID
        selectedFriendId = nil
    }

    // MARK: 拆分视图

    // 地图主体
    private var mapContent:  some View {
        ZStack {

            Map(position: $viewModel.cameraPosition) {
                // 用户当前位置标记
                if let userLocation = viewModel.userLocation {
                    Annotation("我在这里", coordinate: userLocation, anchor: .center) {
                        ZStack {
                            // 外圈脉冲效果
                            Circle()
                                .fill(Color.brandGreen.opacity(0.3))
                                .frame(width: 40, height: 40)
                                .scaleEffect(1.5)
                                .opacity(0.6)
                                .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: userLocation)
                            // TODO: loop的动画：伴随scale和easeOut效果

                            // 内圈位置标记
                            if let userHeading = viewModel.userHeading{
                                Image(systemName: "location.fill")
                                    .foregroundColor(.white)
                                    .font(.title2)
                                    .padding(8)
                                    .rotationEffect(Angle(degrees: userHeading))
                                    .background(
                                        Circle()
                                            .fill(Color.brandGreen)
                                            .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
                                    )
                            }
                        }
                        .opacity(selectedFriendId != nil ? 0.3 : 1)
                    }
                }

                // 好友位置标注
                ForEach(friendMapViewModel.friendAnnotations) { annotation in
                    Annotation(
                        annotation.title,
                        coordinate: annotation.coordinate,
                        anchor: .center
                    ) {
                        FriendOnMapView(
                            friendLocation: annotation.friendLocation,
                            avatarSize: 45,
                            showDetails: true,
                            onCardDropped: { cardId in
                                handleCardDropToFriend(cardId: cardId, userId: annotation.friendLocation.userId)
                            },
                            onPropDropped: { propId in
                                handlePropDropToFriend(propId: propId, userId: annotation.friendLocation.userId)
                            },
                            onShowCardSheet: { friendId in
                                // 设置选中的好友ID
                                selectedFriendId = friendId
                                showBackgroundOverlay = true
                                // 显示卡片sheet
                                showCardOverlay = true
                            },
                            onShowPropSheet: { friendId in
                                // 设置选中的好友ID
                                selectedFriendId = friendId
                                showBackgroundOverlay = true
                                // 显示道具sheet
                                showPropOverlay = true
                            }
                        )
                        // 根据是否选中好友改变透明度
                        .opacity(selectedFriendId != nil && selectedFriendId != annotation.friendLocation.userId ? 0.3 : 1.0)
                        .blur(radius: selectedFriendId != nil && selectedFriendId != annotation.friendLocation.userId ? 0.5 : 0)
                        .animation(.easeInOut(duration: 0.3), value: selectedFriendId)
                        .onTapGesture {
                            // 点击好友标注时的交互逻辑
                            print("🗺️ 点击了好友: \(annotation.friendLocation.nickname)")
                        }
                    }
                }

                // 地点打卡标注
                if (showCheckinAnnotations && selectedFriendId == nil) {
                    ForEach(placeCheckinViewModel.checkinsList) { checkin in
                        Annotation(
                            checkin.displayName.count > 7 ? String(checkin.displayName.prefix(7)) + "..." : checkin.displayName,
                            coordinate: checkin.coordinate,
                            anchor: .center
                        ) {
                            CheckinAnnotationView(checkin: checkin)
                                .onTapGesture {
                                    print("📍 点击了打卡点: \(checkin.displayName)")
                                }
                                .transition(.opacity.combined(with: .scale))
                                .animation(.spring(), value: showCheckinAnnotations)
                        }
                    }
                }
            }
            .mapStyle(viewModel.mapStyle)
            .mapControls{} // 移除自带的地图控件
            .ignoresSafeArea(.all)
            .safeAreaInset(edge: .bottom){
                HStack {
                    // 识别打卡
                    // 照片按钮 - 跳转到 ImageProcessView
                    NavigationLink(destination: ImageProcessView()) {
                        VStack {
                            Image(systemName: "camera")
                                .renderingMode(.template)
                                .frame(width: Theme.IconSize.md, height: Theme.IconSize.md)
                            Text("拍照")
                                .font(.caption)
                        }
                        .foregroundColor(.white)
                        .padding(12)
                    }
                    .buttonStyle(.plain)
                    
                    // 地点打卡
                    Button {
                        showCheckinSheet = true
                    } label: {
                        VStack {
                            Image("svg-checkin")
                                .renderingMode(.template)
                                .frame(width: Theme.IconSize.md, height: Theme.IconSize.md)
                            Text("地点")
                                .font(.caption)
                        }
                        .foregroundColor(.white)
                        .padding(12)
                    }
                    
                    // 出行打卡按钮
                    Button {
                        showFootprintSheet = true
                    } label: {
                        VStack {
                            Image("svg-track")
                                .renderingMode(.template)
                                .frame(width: Theme.IconSize.md, height: Theme.IconSize.md)
                            Text("出行")
                                .font(.caption)
                        }
                        .foregroundColor(.white)
                        .padding(12)
                    }
                }
                .padding(.horizontal, Theme.Spacing.md)
                .glassEffectIfAvailable(.regular)
                .padding(.bottom, Theme.Spacing.md)
            }
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    // 关闭按钮
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                    }
                }
                
                ToolbarItem(placement: .topBarTrailing) {
                    // 传输记录入口
                    Button {
                        showTransferSheet = true
                    } label: {
                        Image(systemName: "arrow.left.arrow.right")
                    }
                }
            }
            .tint(.brandGreen)

            if selectedFriendId != nil {
                Color.clear
                    .ignoresSafeArea()
                    .contentShape(Rectangle())
                    .onTapGesture {
                        closeSheets()
                    }
            }
        }
    }

    
}








#Preview {
    @Previewable @State var appsettings = AppSettings()
    MapView()
        .stableBackground()
        .environmentObject(appsettings)
}
